#!/usr/bin/env python3
"""
Общие утилиты для скриптов tools/.

Устраняет дублирование логики работы с:
- ZIP архивами и извлечением FB2 файлов
- Созданием временных файлов и их очисткой
- Инициализацией проектных компонентов парсинга
- Стандартными паттернами обработки ошибок

Все функции используют только проектные компоненты, никакой самописной логики.
"""

import sys
import tempfile
import zipfile
from contextlib import contextmanager
from pathlib import Path
from typing import Any, Callable, Generator

sys.path.insert(0, str(Path(__file__).parent.parent))

# Импорты проектных компонентов
from app.processing.canonical_model import CanonicalBook
from app.processing.error_handler import QuarantineError
from app.processing.fragment_detector import FragmentDetector
from app.processing.parser_dispatcher import ParserDispatcher


class ToolsComponents:
    """Синглтон для переиспользования проектных компонентов."""

    _instance: "ToolsComponents | None" = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not hasattr(self, "_initialized"):
            self.parser_dispatcher = ParserDispatcher()
            self.fragment_detector = FragmentDetector()
            self._initialized = True


def get_components() -> ToolsComponents:
    """Получить переиспользуемые компоненты проекта."""
    return ToolsComponents()


@contextmanager
def temp_fb2_file() -> Generator[Path, None, None]:
    """
    Контекст-менеджер для создания временного FB2 файла.
    Автоматически удаляет файл при выходе из контекста.
    """
    temp_file = None
    try:
        with tempfile.NamedTemporaryFile(suffix=".fb2", delete=False) as temp_file:
            temp_path = Path(temp_file.name)
        yield temp_path
    finally:
        if temp_file and temp_path.exists():
            temp_path.unlink(missing_ok=True)


def extract_fb2_to_temp(zip_ref: zipfile.ZipFile, fb2_filename: str) -> Path:
    """
    Извлекает FB2 файл из ZIP архива во временный файл.

    Args:
        zip_ref: Открытый ZIP архив
        fb2_filename: Имя FB2 файла в архиве

    Returns:
        Path к временному FB2 файлу

    Raises:
        Exception: При ошибках чтения из архива
    """
    with tempfile.NamedTemporaryFile(suffix=".fb2", delete=False) as temp_file:
        temp_path = Path(temp_file.name)

    # Извлекаем FB2 во временный файл
    with zip_ref.open(fb2_filename) as fb2_file:
        content = fb2_file.read()

    with open(temp_path, "wb") as temp_file:
        temp_file.write(content)

    return temp_path


def get_canonical_book_from_file(fb2_path: Path, parser_dispatcher: ParserDispatcher = None) -> CanonicalBook:
    """
    Получает CanonicalBook из FB2 файла используя проектную логику парсинга.

    Args:
        fb2_path: Путь к FB2 файлу
        parser_dispatcher: Опциональный парсер (если None, создается новый)

    Returns:
        CanonicalBook объект

    Raises:
        QuarantineError: При файлах попавших в карантин
        Exception: При других ошибках парсинга
    """
    if parser_dispatcher is None:
        parser_dispatcher = get_components().parser_dispatcher

    return parser_dispatcher.parse_to_canonical(fb2_path)


def process_zip_archive(
    zip_path: str | Path,
    fb2_processor: Callable[[str, str, Path], Any],
    fb2_filter: Callable[[str], bool] = None,
    error_handler: Callable[[str, str, Exception], None] = None,
) -> list[Any]:
    """
    Универсальная функция для обработки ZIP архивов с FB2 файлами.

    Args:
        zip_path: Путь к ZIP архиву
        fb2_processor: Функция обработки FB2 файла (archive_path, fb2_filename, temp_fb2_path) -> result
        fb2_filter: Опциональный фильтр FB2 файлов (fb2_filename) -> bool
        error_handler: Опциональный обработчик ошибок (archive_path, fb2_filename, exception) -> None

    Returns:
        Список результатов обработки FB2 файлов

    Example:
        def process_fb2(archive_path, fb2_filename, temp_fb2_path):
            canonical_book = get_canonical_book_from_file(temp_fb2_path)
            return {"title": canonical_book.title, "chapters": len(canonical_book.chapters)}

        results = process_zip_archive("/path/to/archive.zip", process_fb2)
    """
    results = []
    zip_path = str(zip_path)

    try:
        with zipfile.ZipFile(zip_path, "r") as zip_ref:
            for file_info in zip_ref.infolist():
                if file_info.filename.lower().endswith(".fb2"):
                    # Применяем фильтр если указан
                    if fb2_filter and not fb2_filter(file_info.filename):
                        continue

                    temp_fb2_path = None
                    try:
                        # Извлекаем FB2 во временный файл
                        temp_fb2_path = extract_fb2_to_temp(zip_ref, file_info.filename)

                        # Обрабатываем файл
                        result = fb2_processor(zip_path, file_info.filename, temp_fb2_path)
                        if result is not None:
                            results.append(result)

                    except QuarantineError:
                        # Тихо пропускаем файлы в карантине
                        pass
                    except Exception as e:
                        # Вызываем обработчик ошибок если указан
                        if error_handler:
                            error_handler(zip_path, file_info.filename, e)
                    finally:
                        # Очищаем временный файл
                        if temp_fb2_path and temp_fb2_path.exists():
                            temp_fb2_path.unlink(missing_ok=True)

    except Exception as e:
        # Ошибка самого ZIP архива
        if error_handler:
            error_handler(zip_path, "", e)

    return results


def safe_cleanup_temp_file(temp_path: Path) -> None:
    """
    Безопасно удаляет временный файл.

    Args:
        temp_path: Путь к временному файлу
    """
    if temp_path and temp_path.exists():
        try:
            temp_path.unlink(missing_ok=True)
        except Exception:
            # Игнорируем ошибки удаления
            pass


def collect_zip_archives(paths: list[str], limit: int = None) -> list[str]:
    """
    Собирает пути ко всем ZIP архивам в указанных директориях.

    Args:
        paths: Список путей для сканирования
        limit: Максимальное количество архивов (None = без лимита)

    Returns:
        Список путей к ZIP архивам
    """
    archive_paths = []

    for scan_path in paths:
        scan_path_obj = Path(scan_path)
        if not scan_path_obj.exists():
            continue

        if scan_path_obj.is_file() and scan_path_obj.suffix.lower() == ".zip":
            archive_paths.append(str(scan_path_obj))
        else:
            # Рекурсивно ищем ZIP файлы
            for zip_file in scan_path_obj.rglob("*.zip"):
                archive_paths.append(str(zip_file))

        # Применяем лимит если указан
        if limit and len(archive_paths) >= limit:
            archive_paths = archive_paths[:limit]
            break

    return archive_paths


def get_fb2_transformer_from_parser(parser_dispatcher: ParserDispatcher):
    """
    Получает FB2Transformer из ParserDispatcher если доступен.

    Args:
        parser_dispatcher: Экземпляр ParserDispatcher

    Returns:
        FB2Transformer или None если недоступен
    """
    if hasattr(parser_dispatcher, "_last_transformer"):
        return parser_dispatcher._last_transformer
    return None


# Вспомогательные функции для стандартных операций
def print_processing_status(file_path: str, status: str, anomaly_types: list[str] = None) -> None:
    """
    Стандартный вывод статуса обработки файла.

    Args:
        file_path: Полный путь к файлу (archive::fb2_filename)
        status: Статус обработки
        anomaly_types: Список обнаруженных аномалий
    """
    anomaly_str = ""
    if anomaly_types:
        anomaly_str = f" ⚠️  ANOMALY: {', '.join(anomaly_types)}"

    print(f"{file_path} - {status}{anomaly_str}")


def format_file_path(archive_path: str, fb2_filename: str) -> str:
    """
    Форматирует путь к файлу для вывода в стандартном формате.

    Args:
        archive_path: Путь к архиву
        fb2_filename: Имя FB2 файла

    Returns:
        Отформатированный путь
    """
    return f"{archive_path}::{fb2_filename}"
