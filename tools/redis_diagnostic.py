#!/usr/bin/env python3
"""
Профессиональный диагностический скрипт для полного анализа Redis.

Показывает всю картину проекта:
- Все очереди и их состояние
- Данные в очередях с анализом структуры
- Статистика и метрики
- Временные метки и даты
- Кэши и сеты
- Проблемы и аномалии

Использование:
python tools/redis_diagnostic.py [--detailed] [--export-json output.json]
"""

import argparse
import json
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, cast

import redis

# Добавляем корневую директорию проекта в sys.path
sys.path.insert(0, str(Path(__file__).parent.parent))

from app import settings


class RedisAnalyzer:
    """Анализатор состояния Redis для проекта books."""

    def __init__(self):
        self.redis_client = redis.from_url(settings.REDIS_URL)
        self.report: dict[str, Any] = {
            "timestamp": time.time(),
            "timestamp_human": datetime.now().isoformat(),
            "queues": {},
            "sets": {},
            "general_info": {},
            "statistics": {},
            "problems": [],
            "samples": {},
        }

    def analyze_all(self) -> dict[str, Any]:
        """Выполняет полный анализ Redis."""

        print("🔍 ДИАГНОСТИКА REDIS - АНАЛИЗ ВСЕГО ПРОЕКТА")
        print("=" * 60)

        # Общая информация
        self._analyze_general_info()

        # Анализ очередей
        self._analyze_queues()

        # Анализ множеств (sets)
        self._analyze_sets()

        # Статистика и метрики
        self._calculate_statistics()

        # Поиск проблем
        self._detect_problems()

        # Образцы данных
        self._collect_samples()

        return self.report

    def _analyze_general_info(self):
        """Анализирует общую информацию о Redis."""

        print("📊 ОБЩАЯ ИНФОРМАЦИЯ REDIS")
        print("-" * 30)

        try:
            info = self.redis_client.info()

            self.report["general_info"] = {
                "redis_version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory_human": info.get("used_memory_human"),
                "used_memory_peak_human": info.get("used_memory_peak_human"),
                "total_commands_processed": info.get("total_commands_processed"),
                "instantaneous_ops_per_sec": info.get("instantaneous_ops_per_sec"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses"),
                "connected_at": datetime.now().isoformat(),
            }

            print(f"✅ Redis версия: {info.get('redis_version')}")
            print(f"✅ Подключенных клиентов: {info.get('connected_clients')}")
            print(f"✅ Использованная память: {info.get('used_memory_human')}")
            print(f"✅ Пик памяти: {info.get('used_memory_peak_human')}")
            print(f"✅ Обработано команд: {info.get('total_commands_processed')}")
            print(f"✅ Операций/сек: {info.get('instantaneous_ops_per_sec')}")

        except Exception as e:
            problems = cast(list[str], self.report["problems"])
            problems.append(f"Ошибка получения общей информации: {e}")
            print(f"❌ Ошибка получения общей информации: {e}")

    def _analyze_queues(self):
        """Анализирует все очереди проекта."""

        print("\n📋 АНАЛИЗ ОЧЕРЕДЕЙ")
        print("-" * 30)

        # Определяем все очереди из настроек
        queue_names = [
            ("QUEUE_PARSING_NEW", settings.QUEUE_PARSING_NEW, "Новые задачи парсинга"),
            (
                "QUEUE_PARSING_PROCESSING",
                settings.QUEUE_PARSING_PROCESSING,
                "Задачи парсинга в обработке",
            ),
            (
                "QUEUE_CHUNKING_NEW",
                settings.QUEUE_CHUNKING_NEW,
                "Новые задачи чанкинга",
            ),
            (
                "QUEUE_CHUNKING_PROCESSING",
                settings.QUEUE_CHUNKING_PROCESSING,
                "Задачи чанкинга в обработке",
            ),
            ("QUEUE_ENRICH_NEW", settings.QUEUE_ENRICH_NEW, "Новые задачи обогащения"),
            (
                "QUEUE_ENRICH_PROCESSING",
                settings.QUEUE_ENRICH_PROCESSING,
                "Задачи обогащения в обработке",
            ),
            (
                "QUEUE_VECTORIZE_NEW",
                settings.QUEUE_VECTORIZE_NEW,
                "Новые задачи векторизации",
            ),
            (
                "QUEUE_VECTORIZE_PROCESSING",
                settings.QUEUE_VECTORIZE_PROCESSING,
                "Задачи векторизации в обработке",
            ),
            ("QUEUE_COMPLETED", settings.QUEUE_COMPLETED, "Завершенные задачи (архив)"),
        ]

        for queue_alias, queue_name, description in queue_names:
            try:
                length = self.redis_client.llen(queue_name)

                queue_info: dict[str, Any] = {
                    "name": queue_name,
                    "alias": queue_alias,
                    "description": description,
                    "length": length,
                    "type": "list",
                    "exists": self.redis_client.exists(queue_name),
                }

                # Анализ содержимого (первые и последние элементы)
                if length > 0:
                    # Первые 3 элемента
                    first_items = cast(list[bytes], self.redis_client.lrange(queue_name, 0, 2))
                    # Последние 3 элемента
                    last_items = cast(list[bytes], self.redis_client.lrange(queue_name, -3, -1))

                    queue_info["first_items"] = [self._parse_json_safely(item) for item in first_items]
                    queue_info["last_items"] = [self._parse_json_safely(item) for item in last_items]

                    # Анализ структуры данных
                    if first_items:
                        sample = self._parse_json_safely(first_items[0])
                        if isinstance(sample, dict):
                            queue_info["data_structure"] = list(sample.keys())
                            queue_info["sample_data"] = sample

                self.report["queues"][queue_alias] = queue_info

                # Вывод информации
                status = "🟢" if length == 0 else f"🔄 {length}"
                print(f"{status} {queue_alias:15} | {queue_name:25} | {description}")
                if length > 0:
                    print(f"    📊 Элементов: {length}")
                    if "data_structure" in queue_info:
                        print(f"    🔧 Структура: {', '.join(queue_info['data_structure'])}")

            except Exception as e:
                error_msg = f"Ошибка анализа очереди {queue_name}: {e}"
                problems = cast(list[str], self.report["problems"])
                problems.append(error_msg)
                print(f"❌ {error_msg}")

    def _analyze_sets(self):
        """Анализирует множества (sets) проекта."""

        print("\n🗂️  АНАЛИЗ МНОЖЕСТВ (SETS)")
        print("-" * 30)

        # Определяем все sets из настроек
        set_names = [
            ("SET_PROCESSED", settings.SET_PROCESSED, "Кэш обработанных файлов"),
            ("SET_QUEUED_IDS", settings.SET_QUEUED_IDS, "ID задач в очередях"),
        ]

        for set_alias, set_name, description in set_names:
            try:
                size = self.redis_client.scard(set_name)

                set_info: dict[str, Any] = {
                    "name": set_name,
                    "alias": set_alias,
                    "description": description,
                    "size": size,
                    "type": "set",
                    "exists": self.redis_client.exists(set_name),
                }

                # Образцы данных (первые 5 элементов)
                if size > 0:
                    samples = cast(list[bytes], self.redis_client.srandmember(set_name, 5))
                    set_info["samples"] = [item.decode() if isinstance(item, bytes) else str(item) for item in samples]

                    # Анализ паттернов
                    if samples:
                        patterns = self._analyze_set_patterns(samples)
                        set_info["patterns"] = patterns

                self.report["sets"][set_alias] = set_info

                # Вывод информации
                status = "🟢" if size == 0 else f"📦 {size}"
                print(f"{status} {set_alias:15} | {set_name:25} | {description}")
                if size > 0:
                    print(f"    📊 Элементов: {size}")
                    if "patterns" in set_info:
                        print(f"    🎯 Паттерны: {', '.join(set_info['patterns'])}")

            except Exception as e:
                error_msg = f"Ошибка анализа множества {set_name}: {e}"
                problems = cast(list[str], self.report["problems"])
                problems.append(error_msg)
                print(f"❌ {error_msg}")

    def _calculate_statistics(self):
        """Рассчитывает статистику и метрики."""

        print("\n📈 СТАТИСТИКА И МЕТРИКИ")
        print("-" * 30)

        stats: dict[str, Any] = {}

        # Суммарная статистика очередей
        queues = cast(dict[str, dict[str, Any]], self.report["queues"])
        sets = cast(dict[str, dict[str, Any]], self.report["sets"])

        total_queued = sum(q.get("length", 0) for q in queues.values())
        total_processed = sets.get("SET_PROCESSED", {}).get("size", 0)
        total_queued_ids = sets.get("SET_QUEUED_IDS", {}).get("size", 0)

        stats["total_tasks_in_queues"] = total_queued
        stats["total_processed_files"] = total_processed
        stats["total_queued_ids"] = total_queued_ids

        # Специфическая статистика очередей
        parsing_new = queues.get("QUEUE_PARSING_NEW", {}).get("length", 0)
        parsing_processing = queues.get("QUEUE_PARSING_PROCESSING", {}).get("length", 0)
        chunking_new = queues.get("QUEUE_CHUNKING_NEW", {}).get("length", 0)
        completed_tasks = queues.get("QUEUE_COMPLETED", {}).get("length", 0)

        stats["new_tasks"] = parsing_new
        stats["processing_tasks"] = parsing_processing
        stats["completed_tasks"] = completed_tasks
        stats["rag_tasks"] = chunking_new

        # Коэффициенты
        if total_processed > 0:
            stats["processing_rate"] = round(parsing_processing / total_processed * 100, 2)
        else:
            stats["processing_rate"] = 0

        stats["queue_balance"] = {
            "new_vs_processing": parsing_new - parsing_processing,
            "completed_vs_rag": completed_tasks - chunking_new,
        }

        self.report["statistics"] = stats

        # Вывод статистики
        print(f"📊 Общих задач в очередях: {total_queued}")
        print(f"📝 Обработанных файлов: {total_processed}")
        print(f"🆔 ID в очередях: {total_queued_ids}")
        print(f"🆕 Новых задач парсинга: {parsing_new}")
        print(f"⚙️  Парсинг в обработке: {parsing_processing}")
        print(f"✅ Завершенных: {completed_tasks}")
        print(f"🤖 Ожидает чанкинга: {chunking_new}")
        print(f"📈 Скорость обработки: {stats['processing_rate']}%")

    def _detect_problems(self):
        """Обнаруживает проблемы и аномалии."""

        print("\n🚨 ОБНАРУЖЕНИЕ ПРОБЛЕМ")
        print("-" * 30)

        problems_list: list[str] = []
        queues = cast(dict[str, dict[str, Any]], self.report["queues"])
        sets = cast(dict[str, dict[str, Any]], self.report["sets"])

        # Проверка застрявших задач в обработке
        parsing_processing = queues.get("QUEUE_PARSING_PROCESSING", {}).get("length", 0)
        if parsing_processing > 10:
            problems_list.append(
                f"Слишком много задач парсинга в обработке: {parsing_processing} (может быть зависание воркеров)"
            )

        # Проверка несоответствия очередей
        parsing_new = queues.get("QUEUE_PARSING_NEW", {}).get("length", 0)
        completed_count = queues.get("QUEUE_COMPLETED", {}).get("length", 0)
        chunking_new = queues.get("QUEUE_CHUNKING_NEW", {}).get("length", 0)

        if completed_count > chunking_new + 10:
            problems_list.append(
                f"Завершенных задач ({completed_count}) намного больше задач чанкинга ({chunking_new})"
            )

        # Проверка несоответствия ID
        queued_ids = sets.get("SET_QUEUED_IDS", {}).get("size", 0)
        total_in_parsing = parsing_new + parsing_processing
        if abs(queued_ids - total_in_parsing) > 5:
            problems_list.append(f"Несоответствие ID в сете ({queued_ids}) и задач парсинга ({total_in_parsing})")

        # Проверка пустых ключевых очередей
        if parsing_new == 0 and parsing_processing == 0:
            problems_list.append("Все очереди парсинга пусты - возможно система простаивает")

        problems = cast(list[str], self.report["problems"])
        problems.extend(problems_list)

        if problems_list:
            for i, problem in enumerate(problems_list, 1):
                print(f"   {i}. {problem}")
        else:
            print("✅ Проблем не обнаружено")

    def _collect_samples(self):
        """Собирает образцы данных для анализа."""

        samples: dict[str, Any] = {}
        queues = cast(dict[str, dict[str, Any]], self.report["queues"])
        sets = cast(dict[str, dict[str, Any]], self.report["sets"])

        # Образцы из очередей
        for queue_alias, queue_info in queues.items():
            if queue_info.get("sample_data"):
                samples[f"queue_{queue_alias.lower()}"] = queue_info["sample_data"]

        # Образцы из sets
        for set_alias, set_info in sets.items():
            if set_info.get("samples"):
                sample_list = cast(list[str], set_info["samples"])
                samples[f"set_{set_alias.lower()}"] = sample_list[:3]  # Первые 3

        self.report["samples"] = samples

    def _parse_json_safely(self, data: Any) -> Any:
        """Безопасно парсит JSON данные."""
        if isinstance(data, bytes):
            data = data.decode()

        try:
            return json.loads(data)
        except (json.JSONDecodeError, TypeError):
            return str(data)

    def _analyze_set_patterns(self, samples: list[Any]) -> list[str]:
        """Анализирует паттерны в данных set."""
        patterns: list[str] = []

        # Анализ образцов
        for sample in samples[:3]:  # Анализируем первые 3
            sample_str = str(sample)

            if ":" in sample_str:
                patterns.append("key:value")
            if sample_str.isdigit():
                patterns.append("numeric")
            if "-" in sample_str and len(sample_str) > 10:
                patterns.append("uuid-like")
            if sample_str.startswith("books:"):
                patterns.append("prefixed")

        return list(set(patterns))  # Уникальные паттерны

    def print_summary(self):
        """Выводит краткую сводку."""

        print("\n📋 КРАТКАЯ СВОДКА")
        print("=" * 60)

        stats = cast(dict[str, Any], self.report["statistics"])

        print("🎯 ОСНОВНЫЕ МЕТРИКИ:")
        print(f"   📊 Всего задач в очередях: {stats.get('total_tasks_in_queues', 0)}")
        print(f"   📝 Обработанных файлов: {stats.get('total_processed_files', 0)}")
        print(f"   🆔 ID в очередях: {stats.get('total_queued_ids', 0)}")

        print("\n🔄 СОСТОЯНИЕ ОЧЕРЕДЕЙ:")
        print(f"   🆕 Новые: {stats.get('new_tasks', 0)}")
        print(f"   ⚙️  Обработка: {stats.get('processing_tasks', 0)}")
        print(f"   ✅ Завершенные: {stats.get('completed_tasks', 0)}")
        print(f"   🤖 RAG: {stats.get('rag_tasks', 0)}")

        problems = cast(list[str], self.report["problems"])
        if problems:
            print(f"\n⚠️  ОБНАРУЖЕННЫЕ ПРОБЛЕМЫ ({len(problems)}):")
            for i, problem in enumerate(problems, 1):
                print(f"   {i}. {problem}")
        else:
            print("\n✅ ПРОБЛЕМ НЕ ОБНАРУЖЕНО")

        print(f"\n🕐 Анализ выполнен: {self.report['timestamp_human']}")

    def export_json(self, filename: str):
        """Экспортирует отчет в JSON файл."""

        with open(filename, "w", encoding="utf-8") as f:
            json.dump(self.report, f, indent=2, ensure_ascii=False, default=str)

        print(f"\n💾 Отчет экспортирован: {filename}")


def main():
    """Основная функция."""

    parser = argparse.ArgumentParser(description="Профессиональная диагностика Redis для проекта books")
    parser.add_argument("--detailed", action="store_true", help="Подробный анализ с образцами данных")
    parser.add_argument("--export-json", help="Экспорт отчета в JSON файл")

    args = parser.parse_args()

    try:
        analyzer = RedisAnalyzer()
        report = analyzer.analyze_all()

        analyzer.print_summary()

        if args.export_json:
            analyzer.export_json(args.export_json)

        # Возвращаем код ошибки если есть проблемы
        return 1 if report["problems"] else 0

    except redis.ConnectionError:
        print("❌ ОШИБКА: Не удается подключиться к Redis")
        print(f"   Проверьте что Redis запущен на {settings.REDIS_URL}")
        return 1
    except Exception as e:
        print(f"❌ КРИТИЧЕСКАЯ ОШИБКА: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
