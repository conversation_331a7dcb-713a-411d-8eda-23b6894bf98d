# run_10_scan_sources.py

import argparse
import logging

import redis
from app import settings
from app.ingestion.scanner import (
    clear_redis_cache,
    scan_and_register_new_files,
    sync_redis_with_db,
)


def main():
    parser = argparse.ArgumentParser(
        description="Сканер источников книг",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Примеры использования:

  Ежедневное сканирование (без кэша):
    python run_10_scan_sources.py

  Тихий режим для cron:
    python run_10_scan_sources.py --quiet

  Сканирование с предварительным кэшированием (для больших дампов):
    python run_10_scan_sources.py --cache

  Очистка Redis кэша:
    python run_10_scan_sources.py --clear-cache

  Только синхронизация кэша (без сканирования):
    python run_10_scan_sources.py --sync-only
        """,
    )

    parser.add_argument(
        "--cache",
        action="store_true",
        help="Предварительно загрузить все обработанные книги в Redis кэш (для больших дампов)",
    )

    parser.add_argument(
        "--clear-cache",
        action="store_true",
        help="Очистить Redis кэш обработанных файлов",
    )

    parser.add_argument(
        "--sync-only",
        action="store_true",
        help="Только синхронизировать кэш с БД, не сканировать файлы",
    )

    parser.add_argument(
        "--quiet",
        "-q",
        action="store_true",
        help="Тихий режим - только критические ошибки",
    )

    args = parser.parse_args()

    # Настраиваем логирование
    # Отключаем варнинги psycopg о rollback соединений
    logging.getLogger("psycopg.pool").setLevel(logging.ERROR)
    logging.getLogger("psycopg").setLevel(logging.ERROR)

    if args.quiet:
        logging.basicConfig(level=logging.WARNING, format="%(message)s")
    else:
        logging.basicConfig(
            level=logging.INFO,
            format="%(message)s",  # Убираем timestamp для читаемости
        )

    redis_client = redis.from_url(settings.REDIS_URL)

    try:
        if args.clear_cache:
            logging.info("🧹 Очистка Redis кэша...")
            clear_redis_cache(redis_client)
            logging.info("✅ Кэш очищен")
            return

        if args.sync_only:
            logging.info("🔄 Синхронизация кэша с БД...")
            sync_redis_with_db(redis_client)
            logging.info("✅ Синхронизация завершена")
            return

        # Основное сканирование
        scan_and_register_new_files(use_cache=args.cache)

    except Exception as e:
        logging.critical(f"💥 Критическая ошибка при сканировании: {e}", exc_info=True)
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
