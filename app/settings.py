# --- File: app/settings.py

import os
import sys
from pathlib import Path

from dotenv import load_dotenv

env_path = Path(__file__).parent.parent / ".env"

if not env_path.exists():
    sys.exit(f"FATAL ERROR: .env file not found at the expected path: {env_path}")

# Добавляем параметр override=True
load_dotenv(dotenv_path=env_path, override=True)

# --- PostgreSQL Settings ---
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = int(os.getenv("POSTGRES_PORT", 65432))
DB_NAME = os.getenv("POSTGRES_DB", "books")
DB_USER = os.getenv("POSTGRES_USER")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD")

# Check PostgreSQL Settings
if not all([DB_HOST, DB_PORT, DB_NAME, DB_USER, DB_PASSWORD]):
    sys.exit("FATAL ERROR: One or more PostgreSQL environment variables are not set in .env")

DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"

# --- Redis Settings ---
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
WORKER_TIMEOUT = int(os.getenv("WORKER_TIMEOUT", 300))
# Максимальный размер очереди завершенных задач (для предотвращения переполнения памяти)
COMPLETED_QUEUE_MAX_SIZE = int(os.getenv("COMPLETED_QUEUE_MAX_SIZE", 100000))

# --- Quarantine Processing Settings ---
# Включение/отключение постобработки файлов в карантине
QUARANTINE_PROCESSING_ENABLED = os.getenv("QUARANTINE_PROCESSING_ENABLED", "true").lower() == "true"

# Redis Queue Names - Стандартизированная схема именования
# 📋 Полная документация: doc/redis_queues.md

# Parsing Pipeline (20)
QUEUE_PARSING_NEW = "books:queue:20_parsing_new"
QUEUE_PARSING_PROCESSING = "books:queue:20_parsing_processing"

# RAG Pipelines
QUEUE_CHUNKING_NEW = "books:queue:30_chunking_new"
QUEUE_CHUNKING_PROCESSING = "books:queue:30_chunking_processing"

QUEUE_ENRICH_NEW = "books:queue:40_enrich_new"
QUEUE_ENRICH_PROCESSING = "books:queue:40_enrich_processing"

QUEUE_VECTORIZE_NEW = "books:queue:50_vectorize_new"
QUEUE_VECTORIZE_PROCESSING = "books:queue:50_vectorize_processing"

# Системные вспомогательные структуры остаются без изменений
QUEUE_COMPLETED = "books:queue:completed"  # Архив завершенных задач
SET_PROCESSED = "books:set:processed"  # Кэш обработанных файлов (source_type:source_id)
SET_QUEUED_IDS = "books:set:queued_ids"  # Быстрая проверка активных задач только для parsing (O(1))

# Миграция на стандартизированную схему именования завершена ✅

# Check Redis Settings
if not REDIS_URL:
    sys.exit("FATAL ERROR: REDIS_URL environment variable is not set in .env")

# --- Source Type Mapping ---
# Карта для преобразования имени директории в числовой тип источника
SOURCE_TYPE_MAP = {"zip_flibusta": 1, "zip_searchfloor": 2, "zip_anna": 3}

# --- 3. Загрузка и проверка путей к источникам ---
_source_dirs_str = os.getenv("SOURCE_DIRS")

if not _source_dirs_str:
    sys.exit("FATAL ERROR: SOURCE_DIRS environment variable is not set or empty in .env")

# Преобразуем строку в список путей
try:
    SOURCE_DIRS = [Path(p.strip()) for p in _source_dirs_str.split(",") if p.strip()]
    if not SOURCE_DIRS:
        raise ValueError("No valid paths found after parsing SOURCE_DIRS.")
except Exception as e:
    sys.exit(f"FATAL ERROR: Could not parse SOURCE_DIRS. Value was: '{_source_dirs_str}'. Error: {e}")


# --- Директории для обработки файлов ---
def get_processing_directories(source_dir: Path) -> dict:
    """Возвращает словарь с путями для обработки файлов конкретного источника"""
    return {
        "source": source_dir,
        "in_progress": source_dir / "in_progress",
        "processed": source_dir / "processed",
        "quarantine": source_dir / "quarantine",
    }


# --- Директория для хранения канонических книг ---
STORAGE_PATH_STR = os.getenv("CANONICAL_STORAGE_PATH", "./storage/canonical_json")
CANONICAL_STORAGE_PATH = Path(STORAGE_PATH_STR)
