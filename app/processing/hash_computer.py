# app/processing/hash_computer.py

import hashlib
import uuid

from app.utils import normalize_for_hash

from .canonical_model import CanonicalBook


class HashComputer:
    """Компьютер хэшей для дедупликации книг.

    Вычисляет единый metadata_hash, который точно идентифицирует
    уникальное произведение + уникальный перевод.

    Использует MD5 UUID для экономии места в БД.
    """

    def get_md5_uuid(self, s: str) -> str:
        """Создает UUID из MD5 хэша строки для экономии места в БД.

        Args:
            s: Строка для хэширования

        Returns:
            UUID строка из MD5 хэша
        """
        # MD5 используется только для генерации UUID, не для безопасности
        return str(uuid.UUID(hashlib.md5(s.encode("utf-8"), usedforsecurity=False).hexdigest()))

    def compute_hashes(self, canonical_book: CanonicalBook) -> dict[str, str]:
        """Вычисляет хэши для дедупликации.

        Args:
            canonical_book: CanonicalBook объект

        Returns:
            Словарь с metadata_hash (content_hash убран)
        """
        metadata_hash = self._compute_metadata_hash(canonical_book)

        # Возвращаем только metadata_hash, content_hash больше не нужен
        return {"metadata_hash": metadata_hash}

    def _compute_metadata_hash(self, canonical_book: CanonicalBook) -> str:
        """Вычисляет единый metadata_hash из нормализованных компонентов.

        Компоненты хэша в порядке:
        1. Название книги (нормализованное)
        2. Авторы (отсортированные по last_name → first_name → middle_name)
        3. Переводчики (отсортированные по last_name → first_name → middle_name)
        4. Серия (выбранная по priority и нормализованная)

        Args:
            canonical_book: Объект CanonicalBook

        Returns:
            MD5 UUID строка
        """
        hash_parts = []

        # 1. Добавляем название книги
        if canonical_book.title:
            title_normalized = normalize_for_hash(canonical_book.title)
            if title_normalized:
                hash_parts.append(title_normalized)

        # 2. Добавляем авторов (сортируем по иерархии полей)
        authors_normalized = self._normalize_and_sort_persons(canonical_book.authors)
        hash_parts.extend(authors_normalized)

        # 3. Добавляем переводчиков (сортируем по иерархии полей)
        translators_normalized = self._normalize_and_sort_persons(canonical_book.translators)
        hash_parts.extend(translators_normalized)

        # 4. Добавляем серию (приоритет серии с номером > 0)
        selected_series = self._select_priority_series(canonical_book.sequences)
        if selected_series:
            series_normalized = normalize_for_hash(selected_series.name)
            if series_normalized:
                hash_parts.append(series_normalized)

        # Создаем финальную строку для хэширования
        metadata_string = "#".join(hash_parts)

        return self.get_md5_uuid(metadata_string)

    def _normalize_and_sort_persons(self, persons) -> list[str]:
        """Нормализует и сортирует список персон (авторов/переводчиков).

        Сортировка по иерархии: last_name → first_name → middle_name

        Args:
            persons: Список CanonicalAuthor объектов

        Returns:
            Отсортированный список нормализованных строк
        """
        if not persons:
            return []

        # Сначала сортируем объекты по иерархии полей
        sorted_persons = sorted(
            persons,
            key=lambda p: (
                normalize_for_hash(p.last_name or ""),
                normalize_for_hash(p.first_name or ""),
                normalize_for_hash(p.middle_name or ""),
            ),
        )

        # Затем нормализуем отсортированный список
        normalized_persons = []
        for person in sorted_persons:
            person_parts = [
                normalize_for_hash(person.last_name or ""),
                normalize_for_hash(person.first_name or ""),
                normalize_for_hash(person.middle_name or ""),
            ]
            # Соединяем части персоны, убирая пустые
            person_string = " ".join(part for part in person_parts if part)
            if person_string:
                normalized_persons.append(person_string)

        return normalized_persons

    def _select_priority_series(self, sequences):
        """Выбирает приоритетную серию из списка.

        Приоритет: серия с номером > 0, иначе первая по порядку.

        Args:
            sequences: Список CanonicalSequence

        Returns:
            CanonicalSequence или None
        """
        if not sequences:
            return None

        # Ищем серию с номером > 0
        for sequence in sequences:
            if sequence.number and sequence.number > 0:
                return sequence

        # Если не найдено, берем первую
        return sequences[0]
