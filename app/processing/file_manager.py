# app/processing/file_manager.py

import logging
import shutil
from pathlib import Path
from typing import Any

from app import settings
from app.processing.error_handler import QuarantineType
from app.processing.quarantine_processor import QuarantineProcessor


class FileManager:
    """Менеджер файлов для атомарных операций перемещения между директориями.
    Обеспечивает отказоустойчивость через атомарные перемещения файлов.
    """

    def __init__(self, enable_quarantine_processing: bool = True):
        self.logger = logging.getLogger(__name__)
        self.quarantine_processor = QuarantineProcessor(enable_processing=enable_quarantine_processing)
        self._ensure_directories()

    def move_to_processing(self, file_path: Path, source_dir: Path) -> Path:
        """Атомарно перемещает файл из исходной директории в /in_progress/.

        Args:
            file_path: Путь к файлу для перемещения
            source_dir: Исходная директория

        Returns:
            Path к файлу в директории обработки

        Raises:
            FileNotFoundError: Если исходный файл не найден
            PermissionError: Если нет прав на перемещение

        """
        dirs = settings.get_processing_directories(source_dir)

        # Путь в директории обработки
        relative_path = file_path.relative_to(dirs["source"])
        in_progress_path = dirs["in_progress"] / relative_path

        # Создаем промежуточные директории
        in_progress_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            # Атомарное перемещение
            shutil.move(str(file_path), str(in_progress_path))
            self.logger.info(f"Файл перемещен в обработку: {file_path.name}")
            return in_progress_path

        except (FileNotFoundError, PermissionError) as e:
            self.logger.error(f"Ошибка перемещения файла {file_path}: {e}")
            raise

    def move_to_processed(self, in_progress_path: Path, source_dir: Path, source_id: int) -> Path:
        """Атомарно перемещает файл из /in_progress/ в /processed/ с группировкой по тысячным значениям ID.

        Группировка по папкам:
        - 826222.zip → /processed/826000/826222.zip
        - 828931.zip → /processed/828000/828931.zip
        - 1234.zip → /processed/1000/1234.zip

        Args:
            in_progress_path: Путь к файлу в директории обработки
            source_dir: Исходная директория для определения структуры папок
            source_id: ID файла для группировки

        Returns:
            Path к файлу в архивной директории

        """
        dirs = settings.get_processing_directories(source_dir)

        # Вычисляем папку по тысячным значениям ID (826222 → 826000)
        from app.utils import get_thousand_folder

        thousand_folder = get_thousand_folder(source_id)

        # Путь в архивной директории с группировкой по тысячам
        processed_path = dirs["processed"] / thousand_folder / in_progress_path.name

        # Создаем промежуточные директории
        processed_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            # Атомарное перемещение
            shutil.move(str(in_progress_path), str(processed_path))
            self.logger.info(f"Файл архивирован: {in_progress_path.name} → {thousand_folder}/")
            return processed_path

        except (FileNotFoundError, PermissionError) as e:
            self.logger.error(f"Ошибка архивации файла {in_progress_path}: {e}")
            raise

    def move_to_quarantine(
        self,
        in_progress_path: Path,
        source_dir: Path,
        quarantine_type: QuarantineType = QuarantineType.ERROR,
        error_reason: str = None,
        claimed_at: str = None,
    ) -> Path:
        """Атомарно перемещает проблемный файл в /quarantine/ с категоризацией.

        Args:
            in_progress_path: Путь к файлу в директории обработки
            source_dir: Исходная директория для определения структуры папок
            quarantine_type: Тип карантина для создания подпапки
            error_reason: Причина помещения в карантин
            claimed_at: Время обработки задачи (для файла ошибки)

        Returns:
            Path к файлу в карантине

        """
        dirs = settings.get_processing_directories(source_dir)

        # Вычисляем относительный путь с учетом типа карантина
        relative_path = in_progress_path.relative_to(dirs["in_progress"])
        quarantine_path = dirs["quarantine"] / quarantine_type.value / relative_path

        # Создаем промежуточные директории
        quarantine_path.parent.mkdir(parents=True, exist_ok=True)

        try:
            # Атомарное перемещение
            shutil.move(str(in_progress_path), str(quarantine_path))

            # Создаем файл с описанием ошибки внутри zip-архива
            if error_reason and quarantine_path.suffix.lower() == ".zip":
                try:
                    import zipfile
                    from tempfile import NamedTemporaryFile

                    # Создаем временный файл для записи ошибки
                    with NamedTemporaryFile(delete=False, suffix=".error") as temp_error_file:
                        error_content = f"Тип карантина: {quarantine_type.value}\nПричина: {error_reason}\nДата: {claimed_at or 'unknown'}"
                        temp_error_file.write(error_content.encode("utf-8"))
                        temp_error_path = Path(temp_error_file.name)

                    # Добавляем файл ошибки в zip-архив
                    with zipfile.ZipFile(quarantine_path, "a") as zip_file:
                        zip_file.write(temp_error_path, arcname=f"{quarantine_path.stem}.error")

                    # Удаляем временный файл
                    temp_error_path.unlink()
                    self.logger.info(f"Файл ошибки добавлен внутрь архива: {quarantine_path.name}")
                except Exception as e:
                    self.logger.error(f"Ошибка при добавлении файла ошибки в архив {quarantine_path.name}: {e}")
                    # Создаем файл с описанием ошибки рядом с архивом (как раньше)
                    error_file = quarantine_path.with_suffix(quarantine_path.suffix + ".error")
                    error_content = f"Тип карантина: {quarantine_type.value}\nПричина: {error_reason}\nДата: {claimed_at or 'unknown'}"
                    error_file.write_text(error_content)
            # Если это не zip-файл, создаем файл ошибки рядом с файлом (как раньше)
            elif error_reason:
                error_file = quarantine_path.with_suffix(quarantine_path.suffix + ".error")
                error_content = (
                    f"Тип карантина: {quarantine_type.value}\nПричина: {error_reason}\nДата: {claimed_at or 'unknown'}"
                )
                error_file.write_text(error_content)

            self.logger.warning(
                f"Файл помещен в карантин [{quarantine_type.value}]: {in_progress_path.name}. Причина: {error_reason}"
            )

            # Постобработка карантина - применяем соответствующую стратегию
            try:
                processing_result = self.quarantine_processor.process_quarantined_file(
                    quarantine_path, quarantine_type, error_reason or "Unknown"
                )

                if processing_result:
                    strategy = processing_result.get("strategy", "unknown")
                    if processing_result.get("status") in ("success", "preserved"):
                        space_saved = processing_result.get("space_saved", 0)
                        if space_saved > 0:
                            self.logger.info(
                                f"Постобработка карантина [{strategy}]: {quarantine_path.name}. "
                                f"Экономия места: {space_saved:,} байт"
                            )
                        else:
                            self.logger.debug(f"Постобработка карантина [{strategy}]: {quarantine_path.name}")
                    else:
                        self.logger.warning(
                            f"Ошибка постобработки карантина: {processing_result.get('error', 'Unknown')}"
                        )

            except Exception as processing_error:
                # Ошибки постобработки не должны влиять на основной процесс карантина
                self.logger.error(
                    f"Критическая ошибка постобработки карантина для {quarantine_path.name}: {processing_error}",
                    exc_info=True,
                )

            return quarantine_path

        except (FileNotFoundError, PermissionError) as e:
            self.logger.error(f"Ошибка перемещения в карантин {in_progress_path}: {e}")
            raise

    def cleanup_temp_files(self, temp_dir: Path):
        """Очищает временные файлы и директории.

        Args:
            temp_dir: Путь к временной директории для удаления

        """
        try:
            if temp_dir.exists() and temp_dir.is_dir():
                shutil.rmtree(temp_dir)
                self.logger.debug(f"Очищена временная директория: {temp_dir}")
        except Exception as e:
            self.logger.warning(f"Ошибка очистки временной директории {temp_dir}: {e}")

    def get_file_info(self, file_path: Path) -> dict[str, Any]:
        """Получает информацию о файле.

        Args:
            file_path: Путь к файлу

        Returns:
            Словарь с информацией о файле

        """
        try:
            stat = file_path.stat()
            return {
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "exists": True,
                "name": file_path.name,
                "suffix": file_path.suffix.lower(),
            }
        except (FileNotFoundError, PermissionError):
            return {"exists": False}

    def _ensure_directories(self):
        """Создает необходимые директории для обработки файлов"""
        for source_dir in settings.SOURCE_DIRS:
            dirs = settings.get_processing_directories(source_dir)
            for dir_path in dirs.values():
                if dir_path != dirs["source"]:  # Исходную директорию не создаем
                    dir_path.mkdir(parents=True, exist_ok=True)
