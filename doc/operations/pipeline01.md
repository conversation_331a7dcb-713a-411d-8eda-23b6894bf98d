# Pipeline 01: Сканер-продюсер источников книг

```bash
# очистить Redis
redis-cli FLUSHDB

# Посомтреть записи в Redis
redis-cli LRANGE books:queue:new 0 -1
redis-cli LRANGE books:queue:processing 0 -1
redis-cli LRANGE books:queue:completed 0 -1

```

## 📖 Назначение

Сканер-продюсер `run_10_scan_sources.py` — первое звено обработки книг в системе. Его задача:

1. Сканировать исходные директории с ZIP архивами книг
2. Извлекать ID файлов (только цифры из имени)
3. **ОПТИМАЛЬНАЯ ПРОВЕРКА ДУБЛИКАТОВ:** быстрые Redis очереди → кэш → PostgreSQL
4. Создавать задачи для воркера-потребителя в Redis очередях

> **📋 Документация очередей**: См. [../redis_queues.md](../redis_queues.md) для полного описания архитектуры очередей Redis.
5. Опционально использовать Redis кэш для ускорения при больших дампах
6. **Проверять ВСЕ 3 очереди:** new, processing, completed для предотвращения дублей

## 🏗️ Архитектура

```
SOURCE_DIRS                    Redis (3 очереди + кэш)              ВОРКЕР
┌─────────────────┐       ┌─────────────────────────────────┐    ┌─────────────────┐
│ zip_flibusta/   │       │ books:queue:new                 │    │ Кэш путей:      │
│ zip_searchfloor/│  -->  │ [{"source_type":1,"source_id":  │    │ {(1,826222):    │
│ zip_anna/       │       │   826222}] (новые, 35 байт)    │--> │  Path(...)}     │
└─────────────────┘       │                                 │    │ O(1) поиск      │
     ^                    │ books:queue:processing          │    │ 0.001 сек       │
     │                    │ [task3] (в работе)              │    └─────────────────┘
     │ кэш путей           │                                 │             │
     │ воркера             │ books:queue:completed           │         обработка
     └─────────────────────│ [task4, task5] (завершенные)    │             │
                          │                                 │             v
                          │ books:set:processed             │      ┌─────────────────┐
                          │ (кэш: БД + completed очередь)   │      │ PostgreSQL      │
                          └─────────────────────────────────┘      │ book_sources    │
                                                                   │ проверка дублей │
                                                                   └─────────────────┘
```

## 📂 Структура задачи

```json
{
  "source_type": 1,
  "source_id": 12345
}
```

- `source_type` — тип источника (1=flibusta, 2=searchfloor, 3=anna)
- `source_id` — ID файла (только цифры, извлеченные регексом)

**Важно**: Полный путь НЕ хранится в задаче для экономии памяти Redis (~70% экономии).

## 🔍 Восстановление путей файлов

**Воркер восстанавливает пути через кэш в памяти:**

1. **При старте воркера**: единократное сканирование всех директорий (~30 сек на 700k файлов)
2. **Построение кэша**: `{(source_type, source_id): Path}` в памяти процесса (~10 MB)  
3. **При обработке**: O(1) поиск файла без файловых операций (0.001 сек вместо 30 сек)

**Обоснование архитектуры:**
- **Экономия Redis**: задачи 35 байт вместо 120 байт
- **Производительность**: 30,000x ускорение поиска файлов  
- **Надежность**: нет зависимости от готовых путей при перемещении файлов
- **Локальность**: все данные в памяти воркера без сетевых запросов

## 🎯 Сценарии использования

### 📅 Ежедневное сканирование (10-20 новых книг)

**Команда:**
```bash
python run_10_scan_sources.py
```

**Характеристики:**
- Запуск: каждые 1-3 часа (cron)
- Файлов: 10-20 новых за день
- Время: ~40ms проверок в PostgreSQL
- RAM: минимальное потребление
- Кэш: НЕ используется (избыточен)

**Производительность:**
```
20 файлов × (0.1ms Redis очереди + 2ms PostgreSQL) = 42ms
Кэш бы загружался 30 секунд = overhead в 714x!
Порядок проверок: Redis очереди (мгновенно) → PostgreSQL
```

### 📦 Ежемесячный дамп (2000-2500 книг)

**Команды:**
```bash
# 1. Загружаем дамп в директории
cp monthly_dump_*.zip /mnt/d/Project/books/zip/zip_flibusta/

# 2. Сканируем с кэшированием
python run_10_scan_sources.py --cache

# 3. После обработки очищаем кэш
python run_10_scan_sources.py --clear-cache

```

**Характеристики:**
- Запуск: ручной, раз в месяц
- Файлов: 2000-2500 новых книг
- Время: 30 сек загрузка кэша + быстрые проверки
- RAM: ~200-300 MB на кэш
- Дубликаты: ~30% (экономия времени)

**Производительность:**
```
Без кэша: 2500 × (0.1ms Redis + 2ms PostgreSQL) = 5.25 секунд
С кэшем: 30 сек загрузка + 2500 × 0.1ms = 30.25 секунд
Выгода при >30% дубликатов за счет пропуска PostgreSQL проверок
Кэш синхронизируется ТОЛЬКО с PostgreSQL (строгий режим)
```

## 🛠️ Команды и опции

### Основные команды

```bash
# Обычное сканирование (ежедневный режим)
python run_10_scan_sources.py

# Сканирование с кэшированием (режим дампа)
python run_10_scan_sources.py --cache

# Только синхронизация кэша (без сканирования)
python run_10_scan_sources.py --sync-only

# Очистка Redis кэша
python run_10_scan_sources.py --clear-cache

# для cron
python run_10_scan_sources.py --quiet

# Справка по всем опциям
python run_10_scan_sources.py --help
```

### Описание опций

| Опция | Назначение | Когда использовать |
|-------|------------|-------------------|
| `--cache` | Предварительно загружает все обработанные файлы из PostgreSQL в Redis кэш (строгий режим) | Большие дампы (>1000 файлов) |
| `--sync-only` | Только синхронизирует кэш с БД, не сканирует файлы | Подготовка кэша, восстановление |
| `--clear-cache` | Очищает Redis кэш books:set:processed, освобождает память | После обработки дампов |
| `--quiet` | Тихий режим - только критические ошибки в вывод | Запуск через cron |

## ⚡ Когда использовать кэширование

### ✅ ИСПОЛЬЗУЙТЕ `--cache` когда:

- **Большие дампы**: >1000 файлов за раз
- **Высокий % дубликатов**: >30% файлов уже обработаны
- **Пакетная загрузка**: разовая обработка архивов
- **Снижение нагрузки на PostgreSQL**: много параллельных процессов

### ❌ НЕ используйте `--cache` когда:

- **Ежедневное сканирование**: 10-100 файлов
- **Редкие запуски**: раз в несколько часов
- **Ограниченная RAM**: <1GB доступной памяти
- **Быстрый PostgreSQL**: SSD, хорошие индексы

## 📊 Мониторинг и логи

### Структура логов

```
🚀 Запуск сканирования источников...
⚡ Режим прямых запросов к БД (для ежедневного сканирования)
📂 Сканируем директорию: /path/zip_flibusta (тип: 1)
📂 Сканируем директорию: /path/zip_searchfloor (тип: 2)
📂 Сканируем директорию: /path/zip_anna (тип: 3)
🏁 Сканирование завершено. Найдено: 150, Новых задач: 25, Пропущено без ID: 5, Пропущено обработанных: 120
```

### Ключевые метрики

- **Найдено**: Общее количество ZIP файлов
- **Новых задач**: Добавлено в очередь для обработки
- **Пропущено без ID**: Файлы без цифрового ID в имени
- **Пропущено обработанных**: Дубликаты из PostgreSQL book_sources
- **Пропущено в очереди**: Дубликаты из Redis очередей (new/processing/completed)

### Рекомендации в логах

```
💡 Совет: для больших дампов используйте --cache для ускорения
💡 Совет: для ежедневного сканирования кэш избыточен, используйте --clear-cache
📊 Использование памяти кэша: 245.7 MB
```

## 🔧 Настройка и конфигурация

### Переменные окружения (.env)

```bash
# Источники данных (через запятую)
SOURCE_DIRS=/mnt/d/Project/books/zip/zip_flibusta,/mnt/d/Project/books/zip/zip_searchfloor,/mnt/d/Project/books/zip/zip_anna

# Redis для очередей и кэша
REDIS_URL=redis://localhost:6379/0

# PostgreSQL для проверки дубликатов
POSTGRES_HOST=localhost
POSTGRES_PORT=65432
POSTGRES_DB=books
POSTGRES_USER=books
POSTGRES_PASSWORD=books_password
```

### Типы источников

```python
SOURCE_TYPE_MAP = {
    'zip_flibusta': 1,     # Flibusta.is
    'zip_searchfloor': 2,  # Поисковая система
    'zip_anna': 3          # Anna's Archive
}
```

### Регулярное выражение для ID

```python
# Извлекает последние цифры перед .zip
ID_EXTRACTOR_REGEX = r'(?:.*\.)?(\d+)\.zip$'

# Примеры:
# book_12345.zip → 12345
# archive-new-2024.zip → 2024
# flibusta.123456.zip → 123456
# prefix.suffix.789012.zip → 789012
```

## 🚀 Автоматизация (Cron)

### Ежедневное сканирование

```bash
# Каждые 3 часа в рабочее время (тихий режим)
0 */3 * * * cd /path/to/books && python run_10_scan_sources.py --quiet >> /var/log/books/scanner.log 2>&1

# Или раз в час  
0 * * * * cd /path/to/books && python run_10_scan_sources.py --quiet >> /var/log/books/scanner.log 2>&1
```

### Еженедельная очистка кэша

```bash
# Воскресенье в 02:00 - очищаем кэш для экономии памяти
0 2 * * 0 cd /path/to/books && python run_10_scan_sources.py --clear-cache >> /var/log/books/scanner.log 2>&1
```

## 🐛 Troubleshooting

### Проблема: Файлы не добавляются в очередь

**Симптомы:**
- `Новых задач: 0` при наличии новых файлов
- Файлы присутствуют в директориях

**Причины и решения:**

1. **Нет цифрового ID в имени файла**
   ```
   Проверьте лог: "Пропущено без ID: X"
   Файлы должны содержать цифры перед .zip
   ```

2. **Файлы уже обработаны**
   ```bash
   # Проверьте в PostgreSQL
   psql $DATABASE_URL -c "SELECT source_type, source_id FROM book_sources ORDER BY source_type, source_id LIMIT 10;"
   ```

3. **Неправильная структура директорий**
   ```bash
   # Проверьте SOURCE_DIRS в .env
   echo $SOURCE_DIRS
   
   # Убедитесь что директории существуют
   ls -la /path/to/sources/
   ```

### Проблема: Redis недоступен

**Симптомы:**
- `❌ Ошибка Redis при сканировании`
- Подключение отклонено

**Решения:**
```bash
# Проверьте Redis
redis-cli ping

# Проверьте URL подключения
echo $REDIS_URL

# Перезапустите Redis
sudo systemctl restart redis
# или
docker restart redis-container
```

### Проблема: PostgreSQL медленно отвечает

**Симптомы:**
- Сканирование долго выполняется
- Таймауты подключений

**Решения:**
```sql
-- Проверьте индекс
\d book_sources

-- Должен быть: uq_source_identity ON book_sources (source_type, source_id)

-- Анализ производительности
EXPLAIN ANALYZE SELECT 1 FROM book_sources WHERE source_type = 1 AND source_id = 12345;

-- Обновите статистику
ANALYZE book_sources;
```

### Проблема: Слишком много памяти Redis

**Симптомы:**
- `📊 Использование памяти кэша: >500 MB`
- Системные предупреждения о памяти

**Решения:**
```bash
# Очистите кэш
python run_10_scan_sources.py --clear-cache

# Проверьте использование памяти Redis
redis-cli INFO memory

# Используйте кэш только для больших дампов
python run_10_scan_sources.py  # без --cache
```

## 📈 Мониторинг производительности

### Ключевые метрики для мониторинга

```bash
# Размеры всех 3 очередей
redis-cli LLEN books:queue:new
redis-cli LLEN books:queue:processing  
redis-cli LLEN books:queue:completed

# Размер кэша (если используется)
redis-cli SCARD books:set:processed

# Количество обработанных файлов в БД
psql $DATABASE_URL -c "SELECT COUNT(*) FROM book_sources;"

# Распределение по источникам
psql $DATABASE_URL -c "SELECT source_type, COUNT(*) FROM book_sources GROUP BY source_type;"
```

### Графики для мониторинга

1. **Размер очереди задач** — должен быстро обнуляться после запуска воркера
2. **Время выполнения сканера** — норма: 1-60 секунд в зависимости от режима
3. **Количество новых задач** — показывает активность источников
4. **Использование RAM Redis** — контроль при использовании кэша

## 🎯 Рекомендации по использованию

### Оптимальная стратегия

1. **Ежедневно**: `python run_10_scan_sources.py` без опций
2. **При дампе**: `python run_10_scan_sources.py --cache`
3. **После дампа**: `python run_10_scan_sources.py --clear-cache`
4. **При проблемах**: `python run_10_scan_sources.py --sync-only`

### Производительность

- **Без кэша**: 50ms на 100 файлов (рекомендуется для <1000 файлов)
- **С кэшем**: 30 сек загрузка + мгновенные проверки (для >1000 файлов)
- **Индекс PostgreSQL**: обеспечивает проверку дубликатов за 1-2ms

### Потребление ресурсов

- **RAM без кэша**: ~10-50 MB
- **RAM с кэшем**: ~200-500 MB (зависит от количества книг в БД)
- **CPU**: минимальное (I/O bound операции)
- **Диск**: только чтение (файлы не перемещаются)

## 📁 Структура архивирования файлов

### Группировка по тысячным значениям ID

Успешно обработанные файлы перемещаются в `/processed/` с автоматической группировкой по папкам:

```
/processed/
├── 0/          # ID от 0 до 999
│   ├── 123.zip
│   └── 789.zip
├── 1000/       # ID от 1000 до 1999  
│   ├── 1234.zip
│   └── 1567.zip
├── 826000/     # ID от 826000 до 826999
│   ├── 826222.zip
│   └── 826789.zipы
└── 828000/     # ID от 828000 до 828999
    ├── 828931.zip
    └── 828456.zip
```

### Логика группировки

```python
# Примеры преобразования ID в папки:
826222 → "826000"  # (826222 // 1000) * 1000 = 826000
828931 → "828000"  # (828931 // 1000) * 1000 = 828000  
1234   → "1000"    # (1234 // 1000) * 1000 = 1000
999    → "0"       # (999 // 1000) * 1000 = 0
50     → "0"       # (50 // 1000) * 1000 = 0
```

### Преимущества

1. **Производительность файловой системы** — ограничение количества файлов в папке
2. **Удобная навигация** — логическое разделение по диапазонам ID
3. **Масштабируемость** — поддержка миллионов файлов без деградации
4. **Совместимость с бэкапами** — простая синхронизация отдельных диапазонов

### Реализация

Группировка выполняется в:
- `FileManager.move_to_processed()` — основной воркер
- `SystemRecovery._recover_orphaned_files()` — скрипт восстановления

---

**Важно**: Сканер НЕ перемещает и НЕ удаляет файлы. Это делает воркер-потребитель (`run_20_process_book_worker.py`) после успешной обработки. 